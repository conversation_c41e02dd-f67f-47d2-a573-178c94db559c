 ✅ Registered theme: Vibrant Dark (vibrant-dark)
 ✅ Registered theme: Vibrant Light (vibrant-light)
 ✅ Registered theme: Neon Dark (neon-dark)
 OC.js Worker initialized
 Colorful themes initialized: Array(3)
 Adeko Lua Editörü b<PERSON>ıldı
 Created welcome file: Untitled
 Active file in group: group-1751746130465-o997cvzmp File: Untitled Content length: 48
 Initializing Monaco Editor with content: -- <PERSON><PERSON>
-- Kodu<PERSON>zu buraya ekleyin

...
 Monaco Editor created successfully
 Editor value: -- <PERSON><PERSON>
-- Kodu<PERSON>zu buraya ekleyin


 Container dimensions: Object
 Applied colorful theme: Vibrant Dark
 ✅ Applied colorful theme: vibrant-dark
 🔄 Forced re-tokenization
 Editor layout forced
 Line 1 tokens: Array(1)
 Line 2 tokens: Array(1)
 Line 3 tokens: Array(1)
 Line 4 tokens: Array(1)
 Creating new file: simple_csg_test.lua with content length: 1743
 Content preview: -- Ultra Simple CSG Test - Single hole in center
-- This script creates the simplest possible CSG op...
 Active file in group: group-1751746130465-o997cvzmp File: simple_csg_test.lua Content length: 1743
 Initializing Monaco Editor with content: -- Ultra Simple CSG Test - Single hole in center
-- This script creates the simplest possible CSG op...
 Monaco Editor created successfully
 Editor value: -- Ultra Simple CSG Test - Single hole in center
-- This script creates the simplest possible CSG operation

-- Material thickness
materialThickness = 18

-- Set door dimensions explicitly
X = 300  -- 300mm width
Y = 400  -- 400mm height

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    -- Create explicit rectangle for the door panel
    G.setLayer("PANEL")
    print("DEBUG: Creating PANEL rectangle from (0,0) to (" .. X .. "," .. Y .. ")")
    G.rectangle({0, 0}, {X, Y})  -- Explicit door rectangle using X,Y variables
    print("DEBUG: PANEL rectangle created")

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Single hole in the exact center of the door
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-10)  -- 10mm deep cut
    -- Create a single circle in the center
    local centerX = X / 2  -- 150mm (center of 300mm width)
    local centerY = Y / 2  -- 200mm (center of 400mm height)
    print("DEBUG: Creating circle at (" .. centerX .. "," .. centerY .. ") with radius 30")
    G.circle({centerX, centerY}, 30)  -- Circle at exact center with 30mm radius
    print("DEBUG: Circle created")

    print("Ultra simple CSG test created:")
    print("- Door panel: " .. X .. "x" .. Y .. "x" .. materialThickness .. "mm")
    print("- Single hole at center (" .. centerX .. "," .. centerY .. ") with 30mm radius")
    print("- Cut depth: 10mm with 20mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

 Container dimensions: Object
 Applied colorful theme: Vibrant Dark
 ✅ Applied colorful theme: vibrant-dark
 🔄 Forced re-tokenization
 Editor layout forced
 Line 1 tokens: Array(1)
 Line 2 tokens: Array(1)
 Line 3 tokens: Array(1)
 Line 4 tokens: Array(1)
 Line 5 tokens: Array(1)
 === SENDING TO RUST ===
 Script content: -- Ultra Simple CSG Test - Single hole in center
-- This script creates the simplest possible CSG operation

-- Material thickness
materialThickness = 18

-- Set door dimensions explicitly
X = 300  -- 300mm width
Y = 400  -- 400mm height

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    -- Create explicit rectangle for the door panel
    G.setLayer("PANEL")
    print("DEBUG: Creating PANEL rectangle from (0,0) to (" .. X .. "," .. Y .. ")")
    G.rectangle({0, 0}, {X, Y})  -- Explicit door rectangle using X,Y variables
    print("DEBUG: PANEL rectangle created")

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Single hole in the exact center of the door
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-10)  -- 10mm deep cut
    -- Create a single circle in the center
    local centerX = X / 2  -- 150mm (center of 300mm width)
    local centerY = Y / 2  -- 200mm (center of 400mm height)
    print("DEBUG: Creating circle at (" .. centerX .. "," .. centerY .. ") with radius 30")
    G.circle({centerX, centerY}, 30)  -- Circle at exact center with 30mm radius
    print("DEBUG: Circle created")

    print("Ultra simple CSG test created:")
    print("- Door panel: " .. X .. "x" .. Y .. "x" .. materialThickness .. "mm")
    print("- Single hole at center (" .. centerX .. "," .. centerY .. ") with 30mm radius")
    print("- Cut depth: 10mm with 20mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

 Lua library path: ./LIBRARY\luaLibrary
 Debug mode: false
 === END SENDING TO RUST ===
 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 0000012974B63D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012974B63D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012974B63D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 0000012974B63D60\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: Creating PANEL rectangle from (0,0) to (500,700)\nDEBUG: PANEL rectangle created\nDEBUG: Creating circle at (250.0,350.0) with radius 30\nDEBUG safe_engine_call: Calling arc with parameters:\n  name=polyline_arc_9\n  cx=290.0\n  cy=446.0\n  radius=30.0\n  start_angle=0.0\n  end_angle=90.0\n  clockwise=false\nDEBUG: Original arc 'polyline_arc_9' with start_angle=0.0 end_angle=90.0 clockwise=false face=top\nDEBUG: Using complementary arc logic\nDEBUG: Final arc 'polyline_arc_9' with start_angle=90.0 end_angle=0.0 clockwise=true\nDEBUG: Stored arc has start_angle=90.0 end_angle=0.0 clockwise=true\nDEBUG safe_engine_call: Calling arc with parameters:\n  name=polyline_arc_10\n  cx=290.0\n  cy=446.0\n  radius=30.0\n  start_angle=90.0\n  end_angle=180.0\n  clockwise=false\nDEBUG: Original arc 'polyline_arc_10' with start_angle=90.0 end_angle=180.0 clockwise=false face=top\nDEBUG: Using complementary arc logic\nDEBUG: Final arc 'polyline_arc_10' with start_angle=180.0 end_angle=90.0 clockwise=true\nDEBUG: Stored arc has start_angle=180.0 end_angle=90.0 clockwise=true\nDEBUG safe_engine_call: Calling arc with parameters:\n  name=polyline_arc_11\n  cx=290.0\n  cy=446.0\n  radius=30.0\n  start_angle=180.0\n  end_angle=270.0\n  clockwise=false\nDEBUG: Original arc 'polyline_arc_11' with start_angle=180.0 end_angle=270.0 clockwise=false face=top\nDEBUG: Using complementary arc logic\nDEBUG: Final arc 'polyline_arc_11' with start_angle=270.0 end_angle=180.0 clockwise=true\nDEBUG: Stored arc has start_angle=270.0 end_angle=180.0 clockwise=true\nDEBUG safe_engine_call: Calling arc with parameters:\n  name=polyline_arc_12\n  cx=290.0\n  cy=446.0\n  radius=30.0\n  start_angle=270.0\n  end_angle=0.0\n  clockwise=false\nDEBUG: Original arc 'polyline_arc_12' with start_angle=270.0 end_angle=0.0 clockwise=false face=top\nDEBUG: Using complementary arc logic\nDEBUG: Final arc 'polyline_arc_12' with start_angle=0.0 end_angle=-90.0 clockwise=true\nDEBUG: Stored arc has start_angle=0.0 end_angle=-90.0 clockwise=true\nDEBUG: Circle created\nUltra simple CSG test created:\n- Door panel: 500x700x18mm\n- Single hole at center (250.0,350.0) with 30mm radius\n- Cut depth: 10mm with 20mm tool\nSwitch to 3D tab to see OpenCascade.js model\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 23:08:58\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"20MM\": {\n      \"paths\": {\n        \"polyline_arc_10\": {\n          \"clockwise\": true,\n          \"endAngle\": 90.0,\n          \"origin\": [\n            290.0,\n            446.0\n          ],\n          \"radius\": 30.0,\n          \"startAngle\": 180.0,\n          \"type\": \"arc\"\n        },\n        \"polyline_arc_11\": {\n          \"clockwise\": true,\n          \"endAngle\": 180.0,\n          \"origin\": [\n            290.0,\n            446.0\n          ],\n          \"radius\": 30.0,\n          \"startAngle\": 270.0,\n          \"type\": \"arc\"\n        },\n        \"polyline_arc_12\": {\n          \"clockwise\": true,\n          \"endAngle\": -90.0,\n          \"origin\": [\n            290.0,\n            446.0\n          ],\n          \"radius\": 30.0,\n          \"startAngle\": 0.0,\n          \"type\": \"arc\"\n        },\n        \"polyline_arc_9\": {\n          \"clockwise\": true,\n          \"endAngle\": 0.0,\n          \"origin\": [\n            290.0,\n            446.0\n          ],\n          \"radius\": 30.0,\n          \"startAngle\": 90.0,\n          \"type\": \"arc\"\n        }\n      }\n    },\n    \"LMM0\": {\n      \"paths\": {\n        \"line_1\": {\n          \"end\": [\n            500.0,\n            0.0\n          ],\n          \"origin\": [\n            0.0,\n            0.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM1\": {\n      \"paths\": {\n        \"line_2\": {\n          \"end\": [\n            500.0,\n            700.0\n          ],\n          \"origin\": [\n            0.0,\n            700.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"LMM2\": {\n      \"paths\": {\n        \"line_3\": {\n          \"end\": [\n            0.0,\n            700.0\n          ],\n          \"origin\":
 Draw commands received: []
 MakerJS JSON received: {
  "models": {
    "20MM": {
      "paths": {
        "polyline_arc_10": {
          "clockwise": true,
          "endAngle": 90.0,
          "origin": [
            290.0,
            446.0
        ...
 Processing makerjs JSON: {
  "models": {
    "20MM": {
      "paths": {
        "polyline_arc_10": {
          "clockwise": true,
          "endAngle": 90.0,
          "origin": [
            290.0,
            446.0
        ...
 Converting arc path: Object
 path.start_angle: undefined
 path.end_angle: undefined
 path.startAngle: 180
 path.endAngle: 90
 Final startAngle: 180
 Final endAngle: 90
 Converting arc path: Object
 path.start_angle: undefined
 path.end_angle: undefined
 path.startAngle: 270
 path.endAngle: 180
 Final startAngle: 270
 Final endAngle: 180
 Converting arc path: Object
 path.start_angle: undefined
 path.end_angle: undefined
 path.startAngle: 0
 path.endAngle: -90
 Final startAngle: 0
 Final endAngle: -90
 Converting arc path: Object
 path.start_angle: undefined
 path.end_angle: undefined
 path.startAngle: 90
 path.endAngle: 0
 Final startAngle: 90
 Final endAngle: 0
 Converted makerjs JSON to 12 draw commands
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
 Drawing 8 commands
 drawCanvas completed
 drawCanvas called with 8 commands
 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: Object
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: Object
OCJSCanvas.vue:154 Door parameters: Object
OCJSCanvas.vue:155 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:172 ⚙️ Sending to worker: Object
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: Object
ocjsWorker.ts:827 Worker received message: createDoorBody
OCJSCanvas.vue:592 OCJSCanvas mounted
ocjsWorker.ts:87 OpenCascade.js initialized in worker
ocjsWorker.ts:103 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:107 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
ocjsWorker.ts:114 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
ocjsWorker.ts:119 ✅ Door body cached with ID: door_1751746149152
ocjsWorker.ts:869 Worker completed: createDoorBody
ocjsService.ts:232 ✅ Door body created successfully
ocjsService.ts:239 🔍 All tool operations: Array(1)
ocjsService.ts:242 🔧 Creating positioned tool shapes for 1 tool operations...
ocjsService.ts:249 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:827 Worker received message: createPositionedToolShapes
ocjsWorker.ts:233 🔧 Tool 20mm End Mill: diameter=20mm, radius=0.01m (10mm)
ocjsWorker.ts:235 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:331 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:869 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 20mm End Mill
ocjsService.ts:274 🔧 Performing sweep operations with 4 positioned tool shapes...
ocjsWorker.ts:827 Worker received message: performSweepOperation
ocjsWorker.ts:496 🔧 Starting sweep operation: subtract
ocjsWorker.ts:515 🔧 Processing 4 tool geometries
ocjsWorker.ts:518 🔍 Starting CSG operations on door body
ocjsWorker.ts:540 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:74  Could not calculate shape volume: BindingError
getShapeVolume @ ocjsWorker.ts:74
ocjsWorker.ts:74  Could not calculate shape volume: BindingError
getShapeVolume @ ocjsWorker.ts:74
ocjsWorker.ts:555 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:556 📊 Volume change: 0.000000 (0.000000 -> 0.000000)
ocjsWorker.ts:540 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:74  Could not calculate shape volume: BindingError
getShapeVolume @ ocjsWorker.ts:74
ocjsWorker.ts:74  Could not calculate shape volume: BindingError
getShapeVolume @ ocjsWorker.ts:74
ocjsWorker.ts:555 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:556 📊 Volume change: 0.000000 (0.000000 -> 0.000000)
ocjsWorker.ts:540 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:74  Could not calculate shape volume: BindingError
getShapeVolume @ ocjsWorker.ts:74
ocjsWorker.ts:74  Could not calculate shape volume: BindingError
getShapeVolume @ ocjsWorker.ts:74
ocjsWorker.ts:555 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:556 📊 Volume change: 0.000000 (0.000000 -> 0.000000)
ocjsWorker.ts:540 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:74  Could not calculate shape volume: BindingError
getShapeVolume @ ocjsWorker.ts:74
ocjsWorker.ts:74  Could not calculate shape volume: BindingError
getShapeVolume @ ocjsWorker.ts:74
ocjsWorker.ts:555 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:556 📊 Volume change: 0.000000 (0.000000 -> 0.000000)
ocjsWorker.ts:584 ✅ Sweep operation completed, result cached with ID: result_1751746150329
ocjsWorker.ts:869 Worker completed: performSweepOperation
ocjsService.ts:281 ✅ Sweep operations completed: result_1751746150329
ocjsService.ts:284 🔧 Exporting final result to GLB...
ocjsWorker.ts:827 Worker received message: exportGLB
ocjsWorker.ts:601 🔧 Exporting to GLB...
ocjsWorker.ts:626 🔍 Exporting final shape to GLB format
ocjsWorker.ts:652 ✅ GLB export completed, size: 11672 bytes
ocjsWorker.ts:869 Worker completed: exportGLB
ocjsService.ts:286 ✅ Final GLB exported, size: 11672 bytes
OCJSCanvas.vue:181 ✅ Worker processing completed, GLB data size: 11672
OCJSCanvas.vue:195 Model URL created: blob:http://localhost:1420/82f448bb-ccb0-4618-a035-c863ca9f3e03
OCJSCanvas.vue:233 ✅ Initializing Three.js scene
OCJSCanvas.vue:234 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:235 📐 Container dimensions: Object
OCJSCanvas.vue:254 Container dimensions: Object
OCJSCanvas.vue:270 Renderer created with size: 1920 x 600
OCJSCanvas.vue:271 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ auto;​">​
OCJSCanvas.vue:272 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:307 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:319 🔄 Loading GLB model: blob:http://localhost:1420/82f448bb-ccb0-4618-a035-c863ca9f3e03
OCJSCanvas.vue:387 Loading progress: 100%
OCJSCanvas.vue:326 GLB model loaded successfully
OCJSCanvas.vue:350 Model bounding box: Object
OCJSCanvas.vue:362 Max dimension: 0.7
OCJSCanvas.vue:366 Camera distance: 2.0999999999999996
OCJSCanvas.vue:378 Model centered and camera adjusted
OCJSCanvas.vue:379 Camera position: _Vector3
OCJSCanvas.vue:380 Model position: _Vector3
OCJSCanvas.vue:470 Wireframe mode: true
VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "20MM": {
      "paths": {
        "polyline_arc_10": {
          "clockwise": true,
          "endAngle": 90.0,
          "origin": [
            290.0,
            446.0
        ...
VisualizationPanel.vue:355 Converting arc path: Object
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 180
VisualizationPanel.vue:359 path.endAngle: 90
VisualizationPanel.vue:365 Final startAngle: 180
VisualizationPanel.vue:366 Final endAngle: 90
VisualizationPanel.vue:355 Converting arc path: Object
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 270
VisualizationPanel.vue:359 path.endAngle: 180
VisualizationPanel.vue:365 Final startAngle: 270
VisualizationPanel.vue:366 Final endAngle: 180
VisualizationPanel.vue:355 Converting arc path: Object
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 0
VisualizationPanel.vue:359 path.endAngle: -90
VisualizationPanel.vue:365 Final startAngle: 0
VisualizationPanel.vue:366 Final endAngle: -90
VisualizationPanel.vue:355 Converting arc path: Object
VisualizationPanel.vue:356 path.start_angle: undefined
VisualizationPanel.vue:357 path.end_angle: undefined
VisualizationPanel.vue:358 path.startAngle: 90
VisualizationPanel.vue:359 path.endAngle: 0
VisualizationPanel.vue:365 Final startAngle: 90
VisualizationPanel.vue:366 Final endAngle: 0
VisualizationPanel.vue:1120 Converted makerjs JSON to 12 draw commands
EditorGroup.vue:102 Active file in group: group-1751746130465-o997cvzmp File: simple_csg_test.lua Content length: 1743
OCJSCanvas.vue:687 OCJSCanvas unmounted
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 301 x 845
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 301 x 845
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
Editor.vue:66 Initializing Monaco Editor with content: -- Ultra Simple CSG Test - Single hole in center
-- This script creates the simplest possible CSG op...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- Ultra Simple CSG Test - Single hole in center
-- This script creates the simplest possible CSG operation

-- Material thickness
materialThickness = 18

-- Set door dimensions explicitly
X = 300  -- 300mm width
Y = 400  -- 400mm height

local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = ADekoLib

    -- Create door panel (PANEL layer - this will be the base door body)
    G.setFace("top")
    G.setThickness(-materialThickness)
    -- Create explicit rectangle for the door panel
    G.setLayer("PANEL")
    print("DEBUG: Creating PANEL rectangle from (0,0) to (" .. X .. "," .. Y .. ")")
    G.rectangle({0, 0}, {X, Y})  -- Explicit door rectangle using X,Y variables
    print("DEBUG: PANEL rectangle created")

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Single hole in the exact center of the door
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-10)  -- 10mm deep cut
    -- Create a single circle in the center
    local centerX = X / 2  -- 150mm (center of 300mm width)
    local centerY = Y / 2  -- 200mm (center of 400mm height)
    print("DEBUG: Creating circle at (" .. centerX .. "," .. centerY .. ") with radius 30")
    G.circle({centerX, centerY}, 30)  -- Circle at exact center with 30mm radius
    print("DEBUG: Circle created")

    print("Ultra simple CSG test created:")
    print("- Door panel: " .. X .. "x" .. Y .. "x" .. materialThickness .. "mm")
    print("- Single hole at center (" .. centerX .. "," .. centerY .. ") with 30mm radius")
    print("- Cut depth: 10mm with 20mm tool")
    print("Switch to 3D tab to see OpenCascade.js model")
end

require "ADekoDebugMode"

Editor.vue:259 Container dimensions: Object
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: Array(1)
Editor.vue:299 Line 2 tokens: Array(1)
Editor.vue:299 Line 3 tokens: Array(1)
Editor.vue:299 Line 4 tokens: Array(1)
Editor.vue:299 Line 5 tokens: Array(1)
GraphicsCanvas.vue:104 drawCanvas called with 8 commands
GraphicsCanvas.vue:138 Canvas size set to: 0 x 0
GraphicsCanvas.vue:170 Drawing 8 commands
GraphicsCanvas.vue:176 drawCanvas completed
