-- Simple test to verify boolean operations are working
-- Creates one large, obvious cut that should be clearly visible

-- Set up basic variables
X = 200  -- Small door for testing
Y = 150
materialThickness = 18

function modelMain()
    print("=== SIMPLE CUT TEST ===")
    
    -- Initialize ADekoLib
    G = AdekoLib
    
    -- Create door panel
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    print("✓ Small door panel created (200x150mm)")
    
    -- Create ONE LARGE, OBVIOUS CUT
    G.setFace("top")
    G<PERSON>setLayer("50MM")  -- Very large tool
    G.setThickness(-15)  -- Very deep cut (almost through the door)
    
    -- Create a large rectangle that takes up most of the door
    G.rectangle({25, 25}, {175, 125})
    print("✓ LARGE CUT: 50mm tool, 15mm deep, covers most of door surface")
    print("✓ Cut area: (25,25) to (175,125) - should be VERY visible")
    
    print("=== SIMPLE CUT TEST COMPLETED ===")
    print("This should create a MASSIVE rectangular pocket in the door")
    print("If you can't see this cut in 3D wireframe, there's a fundamental issue")
    
    return true
end

require "ADekoDebugMode"
