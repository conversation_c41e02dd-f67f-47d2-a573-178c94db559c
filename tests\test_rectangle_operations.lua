-- Test script for rectangle operations
-- Tests various rectangle creation methods and parameters

-- Set up basic variables
X = 300
Y = 200
materialThickness = 18

function modelMain()
    print("=== RECTANGLE OPERATIONS TEST ===")
    
    -- Initialize ADekoLib
    G = ADekoLib
    
    -- Create door panel (PANEL layer)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    print("✓ Door panel created (300x200mm)")
    
    -- TOP SURFACE OPERATIONS
    G.setFace("top")
    
    -- Test 1: Basic rectangle (sharp corners)
    print("Creating basic rectangle...")
    G.setLayer("10MM")  -- 10mm cylindrical tool
    G.setThickness(-5)
    G.rectangle({50, 50}, {150, 100})
    print("✓ Basic rectangle: (50,50) to (150,100), depth -5mm")
    
    -- Test 2: Rectangle with rounded corners (bulge)
    print("Creating rounded rectangle...")
    G<PERSON>setLayer("8MM")   -- 8mm cylindrical tool
    G.setThickness(-3)
    G.rectangle({180, 50}, {280, 100}, 10)  -- bulge = 10 for rounded corners
    print("✓ Rounded rectangle: (180,50) to (280,100), bulge=10, depth -3mm")
    
    -- Test 3: Large rectangle for pocket operation
    print("Creating large pocket rectangle...")
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-8)
    G.rectangle({30, 120}, {270, 170})
    print("✓ Large pocket: (30,120) to (270,170), depth -8mm")
    
    -- Test 4: Small precision rectangle
    print("Creating small precision rectangle...")
    G.setLayer("6MM")   -- 6mm cylindrical tool
    G.setThickness(-2)
    G.rectangle({100, 30}, {120, 40})
    print("✓ Small rectangle: (100,30) to (120,40), depth -2mm")
    
    -- Test 5: Rectangle with different bulge values
    print("Creating rectangles with different bulge values...")
    
    -- Small bulge
    G.setLayer("12MM")
    G.setThickness(-4)
    G.rectangle({20, 180}, {80, 195}, 5)  -- small bulge
    print("✓ Small bulge rectangle: bulge=5")
    
    -- Medium bulge  
    G.rectangle({100, 180}, {160, 195}, 15) -- medium bulge
    print("✓ Medium bulge rectangle: bulge=15")
    
    -- Large bulge
    G.rectangle({180, 180}, {240, 195}, 25) -- large bulge
    print("✓ Large bulge rectangle: bulge=25")
    
    -- BOTTOM SURFACE OPERATIONS
    print("Creating bottom surface rectangles...")
    G.setFace("bottom")
    
    -- Test 6: Bottom face rectangle
    G.setLayer("15MM_SF")  -- SF suffix for bottom face
    G.setThickness(-6)
    G.rectangle({60, 60}, {240, 140})
    print("✓ Bottom face rectangle: (60,60) to (240,140), depth -6mm")
    
    -- Test 7: Bottom face with rounded corners
    G.setLayer("10MM_SF")
    G.setThickness(-3)
    G.rectangle({80, 20}, {220, 50}, 8)
    print("✓ Bottom rounded rectangle: bulge=8, depth -3mm")
    
    print("=== RECTANGLE TEST COMPLETED ===")
    print("Created rectangles with:")
    print("  - Basic sharp corners")
    print("  - Various bulge values (5, 8, 10, 15, 25)")
    print("  - Different depths (-2 to -8mm)")
    print("  - Different tool sizes (6MM to 20MM)")
    print("  - Both top and bottom faces")
    print("Switch to 2D/3D visualization to see results")
    
    return true
end

require "ADekoDebugMode"
