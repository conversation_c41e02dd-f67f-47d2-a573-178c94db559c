colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Dark (vibrant-dark)
colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Light (vibrant-light)
colorfulThemeService.ts:190 ✅ Registered theme: Neon Dark (neon-dark)
ocjsService.ts:74 OC.js Worker initialized
colorfulThemeService.ts:236 Colorful themes initialized: Array(3)
App.vue:1043 Adeko Lua Editörü başlatıldı
App.vue:1057 Created welcome file: Untitled
EditorGroup.vue:102 Active file in group: group-1751744756862-rv3m0l6jh File: Untitled Content length: 48
Editor.vue:66 Initializing Monaco Editor with content: -- <PERSON><PERSON>ı
-- <PERSON><PERSON><PERSON><PERSON> buraya ekleyin

...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- <PERSON><PERSON>
-- <PERSON><PERSON><PERSON><PERSON> bura<PERSON> ekleyin


Editor.vue:259 Container dimensions: Object
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: Array(1)
Editor.vue:299 Line 2 tokens: Array(1)
Editor.vue:299 Line 3 tokens: Array(1)
Editor.vue:299 Line 4 tokens: Array(1)
useEditorState.ts:120 Creating new file: test_rectangle_operations.lua with content length: 3198
useEditorState.ts:121 Content preview: -- Test script for rectangle operations
-- Tests various rectangle creation methods and parameters

...
EditorGroup.vue:102 Active file in group: group-1751744756862-rv3m0l6jh File: test_rectangle_operations.lua Content length: 3198
Editor.vue:66 Initializing Monaco Editor with content: -- Test script for rectangle operations
-- Tests various rectangle creation methods and parameters

...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- Test script for rectangle operations
-- Tests various rectangle creation methods and parameters

-- Set up basic variables
X = 300
Y = 200
materialThickness = 18

function modelMain()
    print("=== RECTANGLE OPERATIONS TEST ===")
    
    -- Initialize ADekoLib
    G = AdekoLib
    
    -- Create door panel (PANEL layer)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    print("✓ Door panel created (300x200mm)")
    
    -- TOP SURFACE OPERATIONS
    G.setFace("top")
    
    -- Test 1: Basic rectangle (sharp corners)
    print("Creating basic rectangle...")
    G.setLayer("10MM")  -- 10mm cylindrical tool
    G.setThickness(-5)
    G.rectangle({50, 50}, {150, 100})
    print("✓ Basic rectangle: (50,50) to (150,100), depth -5mm")
    
    -- Test 2: Rectangle with rounded corners (bulge)
    print("Creating rounded rectangle...")
    G.setLayer("8MM")   -- 8mm cylindrical tool
    G.setThickness(-3)
    G.rectangle({180, 50}, {280, 100}, 10)  -- bulge = 10 for rounded corners
    print("✓ Rounded rectangle: (180,50) to (280,100), bulge=10, depth -3mm")
    
    -- Test 3: Large rectangle for pocket operation
    print("Creating large pocket rectangle...")
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-8)
    G.rectangle({30, 120}, {270, 170})
    print("✓ Large pocket: (30,120) to (270,170), depth -8mm")
    
    -- Test 4: Small precision rectangle
    print("Creating small precision rectangle...")
    G.setLayer("6MM")   -- 6mm cylindrical tool
    G.setThickness(-2)
    G.rectangle({100, 30}, {120, 40})
    print("✓ Small rectangle: (100,30) to (120,40), depth -2mm")
    
    -- Test 5: Rectangle with different bulge values
    print("Creating rectangles with different bulge values...")
    
    -- Small bulge
    G.setLayer("12MM")
    G.setThickness(-4)
    G.rectangle({20, 180}, {80, 195}, 5)  -- small bulge
    print("✓ Small bulge rectangle: bulge=5")
    
    -- Medium bulge  
    G.rectangle({100, 180}, {160, 195}, 15) -- medium bulge
    print("✓ Medium bulge rectangle: bulge=15")
    
    -- Large bulge
    G.rectangle({180, 180}, {240, 195}, 25) -- large bulge
    print("✓ Large bulge rectangle: bulge=25")
    
    -- BOTTOM SURFACE OPERATIONS
    print("Creating bottom surface rectangles...")
    G.setFace("bottom")
    
    -- Test 6: Bottom face rectangle
    G.setLayer("15MM_SF")  -- SF suffix for bottom face
    G.setThickness(-6)
    G.rectangle({60, 60}, {240, 140})
    print("✓ Bottom face rectangle: (60,60) to (240,140), depth -6mm")
    
    -- Test 7: Bottom face with rounded corners
    G.setLayer("10MM_SF")
    G.setThickness(-3)
    G.rectangle({80, 20}, {220, 50}, 8)
    print("✓ Bottom rounded rectangle: bulge=8, depth -3mm")
    
    print("=== RECTANGLE TEST COMPLETED ===")
    print("Created rectangles with:")
    print("  - Basic sharp corners")
    print("  - Various bulge values (5, 8, 10, 15, 25)")
    print("  - Different depths (-2 to -8mm)")
    print("  - Different tool sizes (6MM to 20MM)")
    print("  - Both top and bottom faces")
    print("Switch to 2D/3D visualization to see results")
    
    return true
end

require "ADekoDebugMode"

Editor.vue:259 Container dimensions: Object
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: Array(1)
Editor.vue:299 Line 2 tokens: Array(1)
Editor.vue:299 Line 3 tokens: Array(1)
Editor.vue:299 Line 4 tokens: Array(1)
Editor.vue:299 Line 5 tokens: Array(1)
App.vue:324 Active file changed to: test_rectangle_operations.lua
EditorGroup.vue:102 Active file in group: group-1751744756862-rv3m0l6jh File: test_rectangle_operations.lua Content length: 3197
EditorGroup.vue:102 Active file in group: group-1751744756862-rv3m0l6jh File: test_rectangle_operations.lua Content length: 3198
luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- Test script for rectangle operations
-- Tests various rectangle creation methods and parameters

-- Set up basic variables
X = 300
Y = 200
materialThickness = 18

function modelMain()
    print("=== RECTANGLE OPERATIONS TEST ===")
    
    -- Initialize ADekoLib
    G = ADekoLib
    
    -- Create door panel (PANEL layer)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    print("✓ Door panel created (300x200mm)")
    
    -- TOP SURFACE OPERATIONS
    G.setFace("top")
    
    -- Test 1: Basic rectangle (sharp corners)
    print("Creating basic rectangle...")
    G.setLayer("10MM")  -- 10mm cylindrical tool
    G.setThickness(-5)
    G.rectangle({50, 50}, {150, 100})
    print("✓ Basic rectangle: (50,50) to (150,100), depth -5mm")
    
    -- Test 2: Rectangle with rounded corners (bulge)
    print("Creating rounded rectangle...")
    G.setLayer("8MM")   -- 8mm cylindrical tool
    G.setThickness(-3)
    G.rectangle({180, 50}, {280, 100}, 10)  -- bulge = 10 for rounded corners
    print("✓ Rounded rectangle: (180,50) to (280,100), bulge=10, depth -3mm")
    
    -- Test 3: Large rectangle for pocket operation
    print("Creating large pocket rectangle...")
    G.setLayer("20MM")  -- 20mm cylindrical tool
    G.setThickness(-8)
    G.rectangle({30, 120}, {270, 170})
    print("✓ Large pocket: (30,120) to (270,170), depth -8mm")
    
    -- Test 4: Small precision rectangle
    print("Creating small precision rectangle...")
    G.setLayer("6MM")   -- 6mm cylindrical tool
    G.setThickness(-2)
    G.rectangle({100, 30}, {120, 40})
    print("✓ Small rectangle: (100,30) to (120,40), depth -2mm")
    
    -- Test 5: Rectangle with different bulge values
    print("Creating rectangles with different bulge values...")
    
    -- Small bulge
    G.setLayer("12MM")
    G.setThickness(-4)
    G.rectangle({20, 180}, {80, 195}, 5)  -- small bulge
    print("✓ Small bulge rectangle: bulge=5")
    
    -- Medium bulge  
    G.rectangle({100, 180}, {160, 195}, 15) -- medium bulge
    print("✓ Medium bulge rectangle: bulge=15")
    
    -- Large bulge
    G.rectangle({180, 180}, {240, 195}, 25) -- large bulge
    print("✓ Large bulge rectangle: bulge=25")
    
    -- BOTTOM SURFACE OPERATIONS
    print("Creating bottom surface rectangles...")
    G.setFace("bottom")
    
    -- Test 6: Bottom face rectangle
    G.setLayer("15MM_SF")  -- SF suffix for bottom face
    G.setThickness(-6)
    G.rectangle({60, 60}, {240, 140})
    print("✓ Bottom face rectangle: (60,60) to (240,140), depth -6mm")
    
    -- Test 7: Bottom face with rounded corners
    G.setLayer("10MM_SF")
    G.setThickness(-3)
    G.rectangle({80, 20}, {220, 50}, 8)
    print("✓ Bottom rounded rectangle: bulge=8, depth -3mm")
    
    print("=== RECTANGLE TEST COMPLETED ===")
    print("Created rectangles with:")
    print("  - Basic sharp corners")
    print("  - Various bulge values (5, 8, 10, 15, 25)")
    print("  - Different depths (-2 to -8mm)")
    print("  - Different tool sizes (6MM to 20MM)")
    print("  - Both top and bottom faces")
    print("Switch to 2D/3D visualization to see results")
    
    return true
end

require "ADekoDebugMode"

luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
App.vue:690 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 00000200808ADEB0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000200808ADEB0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000200808ADEB0\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000200808ADEB0\nDEBUG: Engine is available, proceeding with engine calls\n=== RECTANGLE OPERATIONS TEST ===\n✓ Door panel created (300x200mm)\nCreating basic rectangle...\n✓ Basic rectangle: (50,50) to (150,100), depth -5mm\nCreating rounded rectangle...\n✓ Rounded rectangle: (180,50) to (280,100), bulge=10, depth -3mm\nCreating large pocket rectangle...\n✓ Large pocket: (30,120) to (270,170), depth -8mm\nCreating small precision rectangle...\n✓ Small rectangle: (100,30) to (120,40), depth -2mm\nCreating rectangles with different bulge values...\n✓ Small bulge rectangle: bulge=5\n✓ Medium bulge rectangle: bulge=15\n✓ Large bulge rectangle: bulge=25\nCreating bottom surface rectangles...\n✓ Bottom face rectangle: (60,60) to (240,140), depth -6mm\n✓ Bottom rounded rectangle: bulge=8, depth -3mm\n=== RECTANGLE TEST COMPLETED ===\nCreated rectangles with:\n  - Basic sharp corners\n  - Various bulge values (5, 8, 10, 15, 25)\n  - Different depths (-2 to -8mm)\n  - Different tool sizes (6MM to 20MM)\n  - Both top and bottom faces\nSwitch to 2D/3D visualization to see results\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-05 22:46:32\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"10MM\": {\n      \"paths\": {\n        \"rect_bottom_9\": {\n          \"end\": [\n            90.0,\n            196.0\n          ],\n          \"origin\": [\n            190.0,\n            196.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_12\": {\n          \"end\": [\n            90.0,\n            146.0\n          ],\n          \"origin\": [\n            90.0,\n            196.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_11\": {\n          \"end\": [\n            190.0,\n            196.0\n          ],\n          \"origin\": [\n            190.0,\n            146.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_10\": {\n          \"end\": [\n            190.0,\n            146.0\n          ],\n          \"origin\": [\n            90.0,\n            146.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"10MM_SF\": {\n      \"paths\": {\n        \"rect_bottom_41\": {\n          \"end\": [\n            716.0,\n            146.0\n          ],\n          \"origin\": [\n            856.0,\n            146.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_44\": {\n          \"end\": [\n            716.0,\n            116.0\n          ],\n          \"origin\": [\n            716.0,\n            146.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_43\": {\n          \"end\": [\n            856.0,\n            146.0\n          ],\n          \"origin\": [\n            856.0,\n            116.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_42\": {\n          \"end\": [\n            856.0,\n            116.0\n          ],\n          \"origin\": [\n            716.0,\n            116.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"12MM\": {\n      \"paths\": {\n        \"rect_bottom_25\": {\n          \"end\": [\n            60.0,\n            291.0\n          ],\n          \"origin\": [\n            120.0,\n            291.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_bottom_29\": {\n          \"end\": [\n            140.0,\n            291.0\n          ],\n          \"origin\": [\n            200.0,\n            291.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_bottom_33\": {\n          \"end\": [\n            220.0,\n            291.0\n          ],\n          \"origin\": [\n            280.0,\n            291.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_28\": {\n          \"end\": [\n            60.0,\n            276.0\n          ],\n          \"origin\": [\n            60.0,\n            291.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_32\": {\n          \"end\": [\n            140.0,\n            276.0\n          ],\n          \"origin\": [\n            140.0,\n            291.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_36\": 
App.vue:694 Draw commands received: []
App.vue:696 MakerJS JSON received: {
  "models": {
    "10MM": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            90.0,
            196.0
          ],
          "origin": [
            190.0,
            196.0...
VisualizationPanel.vue:1118 Processing makerjs JSON: {
  "models": {
    "10MM": {
      "paths": {
        "rect_bottom_9": {
          "end": [
            90.0,
            196.0
          ],
          "origin": [
            190.0,
            196.0...
VisualizationPanel.vue:1120 Converted makerjs JSON to 44 draw commands
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 40 commands
GraphicsCanvas.vue:138 Canvas size set to: 1920 x 877
GraphicsCanvas.vue:170 Drawing 40 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: Object
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: Object
OCJSCanvas.vue:154 Door parameters: Object
OCJSCanvas.vue:155 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:172 ⚙️ Sending to worker: Object
ocjsService.ts:226 🔧 SWEEP OPERATION: Starting door processing with tools
ocjsService.ts:228 📐 Creating door body with params: Object
ocjsWorker.ts:807 Worker received message: createDoorBody
OCJSCanvas.vue:592 OCJSCanvas mounted
ocjsWorker.ts:73 OpenCascade.js initialized in worker
ocjsWorker.ts:89 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:93 🚪 Door body dimensions: W=0.5m, H=0.7m, T=0.018m
ocjsWorker.ts:100 🚪 Door body centered at origin: X=[-0.25, 0.25], Y=[-0.35, 0.35], Z=[-0.009, 0.009]
ocjsWorker.ts:105 ✅ Door body cached with ID: door_1751744798818
ocjsWorker.ts:849 Worker completed: createDoorBody
ocjsService.ts:232 ✅ Door body created successfully
ocjsService.ts:239 🔍 All tool operations: Array(7)
ocjsService.ts:242 🔧 Creating positioned tool shapes for 7 tool operations...
ocjsService.ts:249 🔧 Processing 10mm End Mill with 4 commands
ocjsWorker.ts:807 Worker received message: createPositionedToolShapes
ocjsWorker.ts:219 🔧 Tool 10mm End Mill: diameter=10mm, radius=0.005m (5mm)
ocjsWorker.ts:221 🔧 Creating 4 positioned cylindrical tool shapes for 10mm End Mill
ocjsWorker.ts:287 🔧 Positioning line tool 0 at: X=-0.1500m, Y=0.0025m, Z=-0.2500m (from 140, 196 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 1 at: X=-0.2000m, Y=0.0025m, Z=-0.2750m (from 90, 171 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 2 at: X=-0.1000m, Y=0.0025m, Z=-0.2750m (from 190, 171 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 3 at: X=-0.1500m, Y=0.0025m, Z=-0.3000m (from 140, 146 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:317 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:849 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 10mm End Mill
ocjsService.ts:249 🔧 Processing 10mm End Mill with 12 commands
ocjsWorker.ts:807 Worker received message: createPositionedToolShapes
ocjsWorker.ts:219 🔧 Tool 10mm End Mill: diameter=10mm, radius=0.005m (5mm)
ocjsWorker.ts:221 🔧 Creating 12 positioned cylindrical tool shapes for 10mm End Mill
ocjsWorker.ts:287 🔧 Positioning line tool 0 at: X=-0.2000m, Y=0.0025m, Z=-0.1550m (from 90, 291 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 1 at: X=-0.1200m, Y=0.0025m, Z=-0.1550m (from 170, 291 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 2 at: X=-0.0400m, Y=0.0025m, Z=-0.1550m (from 250, 291 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 3 at: X=-0.2300m, Y=0.0025m, Z=-0.1625m (from 60, 283.5 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 4 at: X=-0.1500m, Y=0.0025m, Z=-0.1625m (from 140, 283.5 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 5 at: X=-0.0700m, Y=0.0025m, Z=-0.1625m (from 220, 283.5 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 6 at: X=-0.1700m, Y=0.0025m, Z=-0.1625m (from 120, 283.5 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 7 at: X=-0.0900m, Y=0.0025m, Z=-0.1625m (from 200, 283.5 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 8 at: X=-0.0100m, Y=0.0025m, Z=-0.1625m (from 280, 283.5 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 9 at: X=-0.2000m, Y=0.0025m, Z=-0.1700m (from 90, 276 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 10 at: X=-0.1200m, Y=0.0025m, Z=-0.1700m (from 170, 276 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 11 at: X=-0.0400m, Y=0.0025m, Z=-0.1700m (from 250, 276 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:317 ✅ Created 12 positioned tool shapes
ocjsWorker.ts:849 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 12 positioned shapes for 10mm End Mill
ocjsService.ts:249 🔧 Processing 20mm End Mill with 4 commands
ocjsWorker.ts:807 Worker received message: createPositionedToolShapes
ocjsWorker.ts:219 🔧 Tool 20mm End Mill: diameter=20mm, radius=0.01m (10mm)
ocjsWorker.ts:221 🔧 Creating 4 positioned cylindrical tool shapes for 20mm End Mill
ocjsWorker.ts:287 🔧 Positioning line tool 0 at: X=-0.1000m, Y=0.0050m, Z=-0.1800m (from 190, 266 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 1 at: X=-0.2200m, Y=0.0050m, Z=-0.2050m (from 70, 241 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 2 at: X=0.0200m, Y=0.0050m, Z=-0.2050m (from 310, 241 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 3 at: X=-0.1000m, Y=0.0050m, Z=-0.2300m (from 190, 216 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:317 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:849 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 20mm End Mill
ocjsService.ts:249 🔧 Processing 6mm End Mill with 4 commands
ocjsWorker.ts:807 Worker received message: createPositionedToolShapes
ocjsWorker.ts:219 🔧 Tool 6mm End Mill: diameter=6mm, radius=0.003m (3mm)
ocjsWorker.ts:221 🔧 Creating 4 positioned cylindrical tool shapes for 6mm End Mill
ocjsWorker.ts:287 🔧 Positioning line tool 0 at: X=-0.1400m, Y=0.0015m, Z=-0.3100m (from 150, 136 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 1 at: X=-0.1500m, Y=0.0015m, Z=-0.3150m (from 140, 131 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 2 at: X=-0.1300m, Y=0.0015m, Z=-0.3150m (from 160, 131 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 3 at: X=-0.1400m, Y=0.0015m, Z=-0.3200m (from 150, 126 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:317 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:849 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 6mm End Mill
ocjsService.ts:249 🔧 Processing 6mm End Mill with 4 commands
ocjsWorker.ts:807 Worker received message: createPositionedToolShapes
ocjsWorker.ts:219 🔧 Tool 6mm End Mill: diameter=6mm, radius=0.003m (3mm)
ocjsWorker.ts:221 🔧 Creating 4 positioned cylindrical tool shapes for 6mm End Mill
ocjsWorker.ts:287 🔧 Positioning line tool 0 at: X=-0.0200m, Y=0.0015m, Z=-0.2500m (from 270, 196 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 1 at: X=-0.0700m, Y=0.0015m, Z=-0.2750m (from 220, 171 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 2 at: X=0.0300m, Y=0.0015m, Z=-0.2750m (from 320, 171 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 3 at: X=-0.0200m, Y=0.0015m, Z=-0.3000m (from 270, 146 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:317 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:849 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 6mm End Mill
ocjsService.ts:249 🔧 Processing 6mm End Mill with 4 commands
ocjsWorker.ts:807 Worker received message: createPositionedToolShapes
ocjsWorker.ts:219 🔧 Tool 6mm End Mill: diameter=6mm, radius=0.003m (3mm)
ocjsWorker.ts:221 🔧 Creating 4 positioned cylindrical tool shapes for 6mm End Mill
ocjsWorker.ts:287 🔧 Positioning line tool 0 at: X=0.4960m, Y=-0.0015m, Z=-0.3000m (from 786, 146 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 1 at: X=0.4260m, Y=-0.0015m, Z=-0.3150m (from 716, 131 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 2 at: X=0.5660m, Y=-0.0015m, Z=-0.3150m (from 856, 131 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 3 at: X=0.4960m, Y=-0.0015m, Z=-0.3300m (from 786, 116 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:317 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:849 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 6mm End Mill
ocjsService.ts:249 🔧 Processing 6mm End Mill with 4 commands
ocjsWorker.ts:807 Worker received message: createPositionedToolShapes
ocjsWorker.ts:219 🔧 Tool 6mm End Mill: diameter=6mm, radius=0.003m (3mm)
ocjsWorker.ts:221 🔧 Creating 4 positioned cylindrical tool shapes for 6mm End Mill
ocjsWorker.ts:287 🔧 Positioning line tool 0 at: X=0.4960m, Y=-0.0015m, Z=-0.2100m (from 786, 236 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 1 at: X=0.4060m, Y=-0.0015m, Z=-0.2500m (from 696, 196 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 2 at: X=0.5860m, Y=-0.0015m, Z=-0.2500m (from 876, 196 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:287 🔧 Positioning line tool 3 at: X=0.4960m, Y=-0.0015m, Z=-0.2900m (from 786, 156 mm, offset: 40, 96, door: 500x700 mm)
ocjsWorker.ts:317 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:849 Worker completed: createPositionedToolShapes
ocjsService.ts:263 ✅ Created 4 positioned shapes for 6mm End Mill
ocjsService.ts:274 🔧 Performing sweep operations with 36 positioned tool shapes...
ocjsWorker.ts:807 Worker received message: performSweepOperation
ocjsWorker.ts:482 🔧 Starting sweep operation: subtract
ocjsWorker.ts:501 🔧 Processing 36 tool geometries
ocjsWorker.ts:504 🔍 Starting CSG operations on door body
ocjsWorker.ts:526 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:536 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:536 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:536 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:536 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 4...
ocjsWorker.ts:536 ✅ Tool 4 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 5...
ocjsWorker.ts:536 ✅ Tool 5 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 6...
ocjsWorker.ts:536 ✅ Tool 6 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 7...
ocjsWorker.ts:536 ✅ Tool 7 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 8...
ocjsWorker.ts:536 ✅ Tool 8 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 9...
ocjsWorker.ts:536 ✅ Tool 9 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 10...
ocjsWorker.ts:536 ✅ Tool 10 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 11...
ocjsWorker.ts:536 ✅ Tool 11 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 12...
ocjsWorker.ts:536 ✅ Tool 12 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 13...
ocjsWorker.ts:536 ✅ Tool 13 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 14...
ocjsWorker.ts:536 ✅ Tool 14 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 15...
ocjsWorker.ts:536 ✅ Tool 15 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 16...
ocjsWorker.ts:536 ✅ Tool 16 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 17...
ocjsWorker.ts:536 ✅ Tool 17 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 18...
ocjsWorker.ts:536 ✅ Tool 18 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 19...
ocjsWorker.ts:536 ✅ Tool 19 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 20...
ocjsWorker.ts:536 ✅ Tool 20 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 21...
ocjsWorker.ts:536 ✅ Tool 21 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 22...
ocjsWorker.ts:536 ✅ Tool 22 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 23...
ocjsWorker.ts:536 ✅ Tool 23 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 24...
ocjsWorker.ts:536 ✅ Tool 24 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 25...
ocjsWorker.ts:536 ✅ Tool 25 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 26...
ocjsWorker.ts:536 ✅ Tool 26 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 27...
ocjsWorker.ts:536 ✅ Tool 27 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 28...
ocjsWorker.ts:536 ✅ Tool 28 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 29...
ocjsWorker.ts:536 ✅ Tool 29 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 30...
ocjsWorker.ts:536 ✅ Tool 30 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 31...
ocjsWorker.ts:536 ✅ Tool 31 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 32...
ocjsWorker.ts:536 ✅ Tool 32 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 33...
ocjsWorker.ts:536 ✅ Tool 33 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 34...
ocjsWorker.ts:536 ✅ Tool 34 subtracted successfully
ocjsWorker.ts:526 🔧 Attempting to subtract tool 35...
ocjsWorker.ts:536 ✅ Tool 35 subtracted successfully
ocjsWorker.ts:565 ✅ Sweep operation completed, result cached with ID: result_1751744799116
ocjsWorker.ts:849 Worker completed: performSweepOperation
ocjsService.ts:281 ✅ Sweep operations completed: result_1751744799116
ocjsService.ts:284 🔧 Exporting final result to GLB...
ocjsWorker.ts:807 Worker received message: exportGLB
ocjsWorker.ts:582 🔧 Exporting to GLB...
ocjsWorker.ts:607 🔍 Exporting final shape to GLB format
ocjsWorker.ts:632 ✅ GLB export completed, size: 3232 bytes
ocjsWorker.ts:849 Worker completed: exportGLB
ocjsService.ts:286 ✅ Final GLB exported, size: 3232 bytes
OCJSCanvas.vue:181 ✅ Worker processing completed, GLB data size: 3232
OCJSCanvas.vue:195 Model URL created: blob:http://localhost:1420/2c6e620c-3b8c-4979-9a79-3d2e98021920
OCJSCanvas.vue:233 ✅ Initializing Three.js scene
OCJSCanvas.vue:234 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:235 📐 Container dimensions: Object
OCJSCanvas.vue:254 Container dimensions: Object
OCJSCanvas.vue:270 Renderer created with size: 1920 x 600
OCJSCanvas.vue:271 Canvas element: <canvas data-engine=​"three.js r178" width=​"1920" height=​"600" style=​"display:​ block;​ width:​ 1920px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:272 Canvas style: display: block; width: 1920px; height: 600px;
OCJSCanvas.vue:307 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:319 🔄 Loading GLB model: blob:http://localhost:1420/2c6e620c-3b8c-4979-9a79-3d2e98021920
OCJSCanvas.vue:387 Loading progress: 100%
OCJSCanvas.vue:326 GLB model loaded successfully
OCJSCanvas.vue:350 Model bounding box: Object
OCJSCanvas.vue:362 Max dimension: 0.7
OCJSCanvas.vue:366 Camera distance: 2.0999999999999996
OCJSCanvas.vue:378 Model centered and camera adjusted
OCJSCanvas.vue:379 Camera position: _Vector3
OCJSCanvas.vue:380 Model position: _Vector3
